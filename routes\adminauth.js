const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const db = require('../config/database');
const { validateAdminRegistration, validateAdminUpdate, validateAdminId, validateAdminLogin } = require('../middleware/validation');
const { asyncHandler } = require('../middleware/errorHandler');

// Helper function to log admin actions
const logAdminAction = async (adminId, action, affectedTable = null, affectedId = null) => {
  try {
    const logQuery = `
      INSERT INTO adminauditlogs (AdminID, Action, AffectedTable, AffectedID)
      VALUES (?, ?, ?, ?)
    `;
    await db.execute(logQuery, [adminId, action, affectedTable, affectedId]);
  } catch (error) {
    console.error('Failed to log admin action:', error);
  }
};

// POST /register-admin
router.post('/register-admin', validateAdminRegistration, asyncHandler(async (req, res) => {
  const {
    fullName,
    email,
    password,
    role = 'Librarian',
    status = 'Active'
  } = req.body;

  // Hash password
  const hashedPassword = await bcrypt.hash(password, 10);

  const insertQuery = `
    INSERT INTO admins (
      FullName, Email, Password, Role, Status
    ) VALUES (?, ?, ?, ?, ?)
  `;

  const [result] = await db.execute(
    insertQuery,
    [fullName, email, hashedPassword, role, status]
  );

  // Log the admin creation action
  await logAdminAction(result.insertId, `Admin account created - Role: ${role}`, 'admins', result.insertId);

  res.status(201).json({
    success: true,
    message: '✅ Admin registered successfully',
    data: {
      adminID: result.insertId,
      fullName,
      email,
      role,
      status
    }
  });
}));

// POST /login-admin
router.post('/login-admin', validateAdminLogin, asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Find admin by email
  const selectQuery = `SELECT * FROM admins WHERE Email = ? AND Status = 'Active'`;
  const [results] = await db.execute(selectQuery, [email]);

  if (results.length === 0) {
    return res.status(401).json({
      success: false,
      error: '❌ Invalid credentials or account inactive'
    });
  }

  const admin = results[0];

  // Verify password
  const isPasswordValid = await bcrypt.compare(password, admin.Password);
  if (!isPasswordValid) {
    return res.status(401).json({
      success: false,
      error: '❌ Invalid credentials'
    });
  }

  // Log the login action
  await logAdminAction(admin.AdminID, 'Admin login');

  // Remove password from response
  delete admin.Password;

  res.json({
    success: true,
    message: '✅ Admin login successful',
    data: admin
  });
}));

// GET /get-admin/:adminID
router.get('/get-admin/:adminID', validateAdminId, asyncHandler(async (req, res) => {
  const { adminID } = req.params;

  const selectQuery = `SELECT AdminID, FullName, Email, Role, Status, CreatedAt, UpdatedAt FROM admins WHERE AdminID = ?`;
  const [results] = await db.execute(selectQuery, [adminID]);

  if (results.length === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Admin not found'
    });
  }

  res.json({
    success: true,
    message: '✅ Admin found',
    data: results[0]
  });
}));

// GET /get-all-admins
router.get('/get-all-admins', asyncHandler(async (req, res) => {
  const selectQuery = `
    SELECT AdminID, FullName, Email, Role, Status, CreatedAt, UpdatedAt 
    FROM admins 
    ORDER BY Role, FullName ASC
  `;

  const [results] = await db.execute(selectQuery);

  res.json({
    success: true,
    message: '✅ Admins retrieved successfully',
    count: results.length,
    data: results
  });
}));

// GET /get-admins-by-role/:role
router.get('/get-admins-by-role/:role', asyncHandler(async (req, res) => {
  const { role } = req.params;
  
  const validRoles = ['Super Admin', 'Data Center Admin', 'Librarian', 'Librarian Staff'];
  if (!validRoles.includes(role)) {
    return res.status(400).json({
      success: false,
      error: '❌ Invalid role. Valid roles: Super Admin, Data Center Admin, Librarian, Librarian Staff'
    });
  }

  const selectQuery = `
    SELECT AdminID, FullName, Email, Role, Status, CreatedAt, UpdatedAt 
    FROM admins 
    WHERE Role = ? 
    ORDER BY FullName ASC
  `;

  const [results] = await db.execute(selectQuery, [role]);

  res.json({
    success: true,
    message: `✅ ${role}s retrieved successfully`,
    count: results.length,
    data: results
  });
}));

// PUT /update-admin/:adminID
router.put('/update-admin/:adminID', validateAdminUpdate, asyncHandler(async (req, res) => {
  const { adminID } = req.params;
  const {
    fullName,
    email,
    password,
    role,
    status
  } = req.body;

  // Check if admin exists
  const checkQuery = `SELECT * FROM admins WHERE AdminID = ?`;
  const [results] = await db.execute(checkQuery, [adminID]);

  if (results.length === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Admin not found'
    });
  }

  const currentAdmin = results[0];

  // Build update query dynamically
  let updateQuery = `UPDATE admins SET `;
  let queryParams = [];
  let updateFields = [];

  if (fullName) {
    updateFields.push('FullName = ?');
    queryParams.push(fullName);
  }
  if (email) {
    updateFields.push('Email = ?');
    queryParams.push(email);
  }
  if (password) {
    const hashedPassword = await bcrypt.hash(password, 10);
    updateFields.push('Password = ?');
    queryParams.push(hashedPassword);
  }
  if (role) {
    updateFields.push('Role = ?');
    queryParams.push(role);
  }
  if (status) {
    updateFields.push('Status = ?');
    queryParams.push(status);
  }

  if (updateFields.length === 0) {
    return res.status(400).json({
      success: false,
      error: '❌ No fields to update'
    });
  }

  updateQuery += updateFields.join(', ') + ' WHERE AdminID = ?';
  queryParams.push(adminID);

  const [updateResult] = await db.execute(updateQuery, queryParams);

  if (updateResult.affectedRows === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Admin not found'
    });
  }

  // Log the update action
  const changes = [];
  if (fullName && fullName !== currentAdmin.FullName) changes.push(`Name: ${currentAdmin.FullName} → ${fullName}`);
  if (email && email !== currentAdmin.Email) changes.push(`Email: ${currentAdmin.Email} → ${email}`);
  if (role && role !== currentAdmin.Role) changes.push(`Role: ${currentAdmin.Role} → ${role}`);
  if (status && status !== currentAdmin.Status) changes.push(`Status: ${currentAdmin.Status} → ${status}`);
  if (password) changes.push('Password updated');

  await logAdminAction(adminID, `Admin profile updated: ${changes.join(', ')}`, 'admins', adminID);

  res.json({
    success: true,
    message: '✅ Admin updated successfully',
    data: {
      adminID,
      fullName: fullName || currentAdmin.FullName,
      email: email || currentAdmin.Email,
      role: role || currentAdmin.Role,
      status: status || currentAdmin.Status
    }
  });
}));

// DELETE /delete-admin/:adminID
router.delete('/delete-admin/:adminID', validateAdminId, asyncHandler(async (req, res) => {
  const { adminID } = req.params;

  // Check if admin exists
  const checkQuery = `SELECT * FROM admins WHERE AdminID = ?`;
  const [results] = await db.execute(checkQuery, [adminID]);

  if (results.length === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Admin not found'
    });
  }

  const admin = results[0];

  // Prevent deletion of Super Admin if it's the last one
  if (admin.Role === 'Super Admin') {
    const superAdminCountQuery = `SELECT COUNT(*) as count FROM admins WHERE Role = 'Super Admin' AND Status = 'Active'`;
    const [countResult] = await db.execute(superAdminCountQuery);

    if (countResult[0].count <= 1) {
      return res.status(400).json({
        success: false,
        error: '❌ Cannot delete the last active Super Admin'
      });
    }
  }

  // Log the deletion action before deleting
  await logAdminAction(adminID, `Admin account deleted - ${admin.FullName} (${admin.Role})`, 'admins', adminID);

  const deleteQuery = `DELETE FROM admins WHERE AdminID = ?`;
  const [deleteResult] = await db.execute(deleteQuery, [adminID]);

  if (deleteResult.affectedRows === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Admin not found'
    });
  }

  res.json({
    success: true,
    message: '✅ Admin deleted successfully',
    data: {
      adminID,
      deletedAdmin: {
        fullName: admin.FullName,
        email: admin.Email,
        role: admin.Role
      }
    }
  });
}));

// GET /admin-audit-logs/:adminID?
router.get('/admin-audit-logs/:adminID?', asyncHandler(async (req, res) => {
  const { adminID } = req.params;
  const { limit = 50, offset = 0 } = req.query;

  let selectQuery = `
    SELECT
      aal.LogID,
      aal.AdminID,
      a.FullName as AdminName,
      a.Role as AdminRole,
      aal.Action,
      aal.AffectedTable,
      aal.AffectedID,
      aal.Timestamp
    FROM adminauditlogs aal
    LEFT JOIN admins a ON aal.AdminID = a.AdminID
  `;

  let queryParams = [];

  if (adminID) {
    selectQuery += ` WHERE aal.AdminID = ?`;
    queryParams.push(adminID);
  }

  selectQuery += ` ORDER BY aal.Timestamp DESC LIMIT ? OFFSET ?`;
  queryParams.push(parseInt(limit), parseInt(offset));

  const [results] = await db.execute(selectQuery, queryParams);

  res.json({
    success: true,
    message: '✅ Admin audit logs retrieved successfully',
    count: results.length,
    data: results
  });
}));

// POST /change-admin-password/:adminID
router.post('/change-admin-password/:adminID', validateAdminId, asyncHandler(async (req, res) => {
  const { adminID } = req.params;
  const { currentPassword, newPassword } = req.body;

  // Validate input
  if (!currentPassword || !newPassword) {
    return res.status(400).json({
      success: false,
      error: '❌ Current password and new password are required'
    });
  }

  // Validate new password strength
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}$/;
  if (!passwordRegex.test(newPassword)) {
    return res.status(400).json({
      success: false,
      error: '❌ New password must be at least 6 characters long and contain at least one lowercase letter, one uppercase letter, and one number'
    });
  }

  // Get current admin
  const selectQuery = `SELECT * FROM admins WHERE AdminID = ?`;
  const [results] = await db.execute(selectQuery, [adminID]);

  if (results.length === 0) {
    return res.status(404).json({
      success: false,
      error: '❌ Admin not found'
    });
  }

  const admin = results[0];

  // Verify current password
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, admin.Password);
  if (!isCurrentPasswordValid) {
    return res.status(401).json({
      success: false,
      error: '❌ Current password is incorrect'
    });
  }

  // Hash new password
  const hashedNewPassword = await bcrypt.hash(newPassword, 10);

  // Update password
  const updateQuery = `UPDATE admins SET Password = ? WHERE AdminID = ?`;
  await db.execute(updateQuery, [hashedNewPassword, adminID]);

  // Log the password change
  await logAdminAction(adminID, 'Password changed', 'admins', adminID);

  res.json({
    success: true,
    message: '✅ Password changed successfully'
  });
}));

module.exports = router;

/*

ADMIN AUTHENTICATION API ENDPOINTS - API v1

POST /register-admin
http://localhost:3000/api/v1/adminauth/register-admin
{
  "fullName": "Nathaniel Inocando",
  "email": "<EMAIL>",
  "password": "HelloNathan123",
  "role": "SuperAdmin",
  "status": "Active"
}

POST /login-admin
http://localhost:3000/api/v1/adminauth/login-admin
{
  "email": "<EMAIL>",
  "password": "HelloNathan123"
}

GET /get-admin/:adminID
http://localhost:3000/api/v1/adminauth/get-admin/1

GET /get-all-admins
http://localhost:3000/api/v1/adminauth/get-all-admins

GET /get-admins-by-role/:role
http://localhost:3000/api/v1/adminauth/get-admins-by-role/Super%20Admin
http://localhost:3000/api/v1/adminauth/get-admins-by-role/Data%20Center%20Admin
http://localhost:3000/api/v1/adminauth/get-admins-by-role/Librarian
http://localhost:3000/api/v1/adminauth/get-admins-by-role/Librarian%20Staff

PUT /update-admin/:adminID
http://localhost:3000/api/v1/adminauth/update-admin/1
{
  "fullName": "Nathaniel Updated",
  "email": "<EMAIL>",
  "role": "Data Center Admin",
  "status": "Active"
}

DELETE /delete-admin/:adminID
http://localhost:3000/api/v1/adminauth/delete-admin/1

GET /admin-audit-logs/:adminID?
http://localhost:3000/api/v1/adminauth/admin-audit-logs
http://localhost:3000/api/v1/adminauth/admin-audit-logs/1
http://localhost:3000/api/v1/adminauth/admin-audit-logs?limit=100&offset=0

POST /change-admin-password/:adminID
http://localhost:3000/api/v1/adminauth/change-admin-password/1
{
  "currentPassword": "HelloNathan123",
  "newPassword": "Helloworld-UPDATED-NGA-PASSWORD"
}

ADMIN ROLES:
- Super Admin: Full system access, can manage all admins and system settings
- Data Center Admin: Manages data, reports, and system maintenance
- Librarian: Manages books, transactions, and library operations
- Librarian Staff: Basic library operations, limited administrative access

*/
